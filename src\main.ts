import './assets/main.css'

import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createPinia } from 'pinia'

import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import axios from "axios";
//每次发送请求时的默认地址的前缀:axios.create({baseURL:''})
axios.defaults.baseURL='http://localhost:8080'
// axios.defaults.withCredentials=true  // 暂时注释掉，避免CORS问题
axios.defaults.headers.post['Content-Type'] = 'application/json'
axios.defaults.headers.common['Accept'] = 'application/json'


const app = createApp(App)
app.config.globalProperties.$url = 'http://localhost:8080'
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

app.use(ElementPlus, {
    locale: zhCn,
})

app.use(createPinia())
app.use(router)

app.mount('#app')
