<template>
  <el-cascader
    v-model="selectedValues"
    :options="pcaOptions"
    :props="cascaderProps"
    placeholder="请选择省市区"
    clearable
    filterable
    @change="handleChange"
    style="width: 100%"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { projectApi, type Pca } from '@/api/project'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue?: string[]
  province?: string
  city?: string
  district?: string
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', province: string, city: string, district: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedValues = ref<string[]>([])
const pcaOptions = ref<any[]>([])

// 级联选择器配置
const cascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children',
  emitPath: true, // 返回完整路径
  checkStrictly: false // 严格模式，只能选择叶子节点
}

// 监听外部传入的值变化
watch(() => [props.province, props.city, props.district], ([province, city, district]) => {
  if (province && city && district) {
    selectedValues.value = [province, city, district]
  }
}, { immediate: true })

watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    selectedValues.value = [...newVal]
  }
}, { immediate: true })

// 处理选择变化
const handleChange = (value: string[]) => {
  selectedValues.value = value || []
  emit('update:modelValue', selectedValues.value)

  if (value && value.length >= 1) {
    const province = value[0] || ''
    const city = value[1] || ''
    const district = value[2] || ''
    emit('change', province, city, district)
  } else {
    emit('change', '', '', '')
  }
}

// 后端已经返回了正确的树形结构，不需要额外处理
const processData = (data: Pca[]): Pca[] => {
  // 清理空的children数组
  const cleanEmptyChildren = (nodes: Pca[]) => {
    nodes.forEach(node => {
      if (node.children && node.children.length === 0) {
        delete node.children
      } else if (node.children) {
        cleanEmptyChildren(node.children)
      }
    })
  }

  cleanEmptyChildren(data)
  return data
}

// 加载省市区数据
const loadPcaData = async () => {
  try {
    const response = await projectApi.queryPca()
    const pcaData = response.data

    console.log('省市区原始数据:', pcaData)

    // 后端已经返回了正确的树形结构
    pcaOptions.value = processData(pcaData)

    console.log('省市区处理后数据:', pcaOptions.value)
  } catch (error) {
    console.error('加载省市区数据失败:', error)
    ElMessage.error('加载省市区数据失败')
  }
}

onMounted(() => {
  loadPcaData()
})
</script>

<style scoped>
.el-cascader {
  width: 100%;
}
</style>
