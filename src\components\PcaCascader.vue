<template>
  <div class="pca-cascader">
    <el-cascader
      v-model="selectedValues"
      :options="pcaOptions"
      :props="cascaderProps"
      placeholder="请选择省市区"
      clearable
      filterable
      @change="handleChange"
      style="width: 100%"
      :loading="loading"
    />
    <div v-if="selectedText" class="selected-text">
      已选择：{{ selectedText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { projectApi, type Pca } from '@/api/project'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue?: string[]
  // 支持传入完整的省市区字符串进行解析
  fullAddress?: string
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', fullRegion: string, province: string, city: string, district: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedValues = ref<string[]>([])
const pcaOptions = ref<Pca[]>([])
const loading = ref(false)

// 级联选择器配置
const cascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children',
  emitPath: true, // 返回完整路径
  checkStrictly: false // 只能选择叶子节点
}

// 计算选中的文本
const selectedText = computed(() => {
  if (selectedValues.value && selectedValues.value.length > 0) {
    return selectedValues.value.join('')
  }
  return ''
})

// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    selectedValues.value = [...newVal]
  }
}, { immediate: true })

// 监听完整地址变化，尝试解析
watch(() => props.fullAddress, (newVal) => {
  if (newVal && pcaOptions.value.length > 0) {
    parseFullAddress(newVal)
  }
}, { immediate: true })

// 处理选择变化
const handleChange = (value: string[]) => {
  selectedValues.value = value || []
  emit('update:modelValue', selectedValues.value)

  if (value && value.length >= 1) {
    const province = value[0] || ''
    const city = value[1] || ''
    const district = value[2] || ''
    const fullRegion = value.join('')
    emit('change', fullRegion, province, city, district)
  } else {
    emit('change', '', '', '', '')
  }
}

// 解析完整地址，尝试匹配省市区
const parseFullAddress = (fullAddress: string) => {
  if (!fullAddress || pcaOptions.value.length === 0) return

  // 尝试从省市区数据中匹配
  for (const province of pcaOptions.value) {
    if (fullAddress.includes(province.value)) {
      if (province.children) {
        for (const city of province.children) {
          if (fullAddress.includes(city.value)) {
            if (city.children) {
              for (const district of city.children) {
                if (fullAddress.includes(district.value)) {
                  selectedValues.value = [province.value, city.value, district.value]
                  return
                }
              }
            }
            selectedValues.value = [province.value, city.value]
            return
          }
        }
      }
      selectedValues.value = [province.value]
      return
    }
  }
}

// 后端已经返回了正确的树形结构，不需要额外处理
const processData = (data: Pca[]): Pca[] => {
  // 清理空的children数组
  const cleanEmptyChildren = (nodes: Pca[]) => {
    nodes.forEach(node => {
      if (node.children && node.children.length === 0) {
        delete node.children
      } else if (node.children) {
        cleanEmptyChildren(node.children)
      }
    })
  }

  cleanEmptyChildren(data)
  return data
}

// 加载省市区数据
const loadPcaData = async () => {
  loading.value = true
  try {
    console.log('开始加载省市区数据...')
    const response = await projectApi.queryPca()
    const pcaData = response.data

    console.log('省市区原始数据:', pcaData)

    if (pcaData && Array.isArray(pcaData) && pcaData.length > 0) {
      // 后端已经返回了正确的树形结构
      pcaOptions.value = processData(pcaData)
      console.log('省市区处理后数据:', pcaOptions.value)

      // 如果有传入的完整地址，尝试解析
      if (props.fullAddress) {
        parseFullAddress(props.fullAddress)
      }
    } else {
      console.warn('省市区数据为空或格式不正确')
      ElMessage.warning('省市区数据为空，请检查后端接口')
      // 提供一些测试数据
      pcaOptions.value = [
        {
          value: '山东省',
          label: '山东省',
          children: [
            {
              value: '济南市',
              label: '济南市',
              children: [
                { value: '历下区', label: '历下区' },
                { value: '市中区', label: '市中区' },
                { value: '槐荫区', label: '槐荫区' }
              ]
            }
          ]
        }
      ]
    }
  } catch (error) {
    console.error('加载省市区数据失败:', error)
    ElMessage.error('加载省市区数据失败，使用测试数据')
    // 提供测试数据
    pcaOptions.value = [
      {
        value: '山东省',
        label: '山东省',
        children: [
          {
            value: '济南市',
            label: '济南市',
            children: [
              { value: '历下区', label: '历下区' },
              { value: '市中区', label: '市中区' },
              { value: '槐荫区', label: '槐荫区' }
            ]
          }
        ]
      },
      {
        value: '北京市',
        label: '北京市',
        children: [
          {
            value: '朝阳区',
            label: '朝阳区'
          },
          {
            value: '海淀区',
            label: '海淀区'
          }
        ]
      }
    ]
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadPcaData()
})
</script>

<style scoped>
.pca-cascader {
  width: 100%;
}

.el-cascader {
  width: 100%;
}

.selected-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
