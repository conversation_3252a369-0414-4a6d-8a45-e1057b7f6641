<template>
  <div class="pca-cascader">
    <el-cascader
      v-model="selectedValues"
      :options="pcaOptions"
      :props="cascaderProps"
      placeholder="请选择省市区"
      clearable
      filterable
      @change="handleChange"
      style="width: 100%"
      :loading="loading"
    />
    <div v-if="selectedText" class="selected-text">
      已选择：{{ selectedText }}
    </div>
    <div v-if="pcaOptions.length === 0" class="loading-text">
      正在加载省市区数据...
    </div>
    <div v-else class="debug-info">
      数据已加载：{{ pcaOptions.length }} 个省份
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { projectApi, type Pca } from '@/api/project'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue?: string[]
  // 支持传入完整的省市区字符串进行解析
  fullAddress?: string
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', fullRegion: string, province: string, city: string, district: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedValues = ref<string[]>([])
const pcaOptions = ref<Pca[]>([])
const loading = ref(false)

// 级联选择器配置
const cascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children',
  emitPath: true, // 返回完整路径
  checkStrictly: false, // 只能选择叶子节点
  expandTrigger: 'click' as const // 点击展开
}

// 计算选中的文本
const selectedText = computed(() => {
  if (selectedValues.value && selectedValues.value.length > 0) {
    return selectedValues.value.join('')
  }
  return ''
})

// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    selectedValues.value = [...newVal]
  }
}, { immediate: true })

// 监听完整地址变化，尝试解析
watch(() => props.fullAddress, (newVal) => {
  if (newVal && pcaOptions.value.length > 0) {
    parseFullAddress(newVal)
  }
}, { immediate: true })

// 处理选择变化
const handleChange = (value: string[]) => {
  selectedValues.value = value || []
  emit('update:modelValue', selectedValues.value)

  if (value && value.length >= 1) {
    const province = value[0] || ''
    const city = value[1] || ''
    const district = value[2] || ''
    const fullRegion = value.join('')
    emit('change', fullRegion, province, city, district)
  } else {
    emit('change', '', '', '', '')
  }
}

// 解析完整地址，尝试匹配省市区
const parseFullAddress = (fullAddress: string) => {
  if (!fullAddress || pcaOptions.value.length === 0) return

  // 尝试从省市区数据中匹配
  for (const province of pcaOptions.value) {
    if (fullAddress.includes(province.value)) {
      if (province.children) {
        for (const city of province.children) {
          if (fullAddress.includes(city.value)) {
            if (city.children) {
              for (const district of city.children) {
                if (fullAddress.includes(district.value)) {
                  selectedValues.value = [province.value, city.value, district.value]
                  return
                }
              }
            }
            selectedValues.value = [province.value, city.value]
            return
          }
        }
      }
      selectedValues.value = [province.value]
      return
    }
  }
}

// 处理省市区数据，修复区县数据格式问题
const processData = (data: Pca[]): Pca[] => {
  console.log('开始处理省市区数据:', data)

  if (!data || !Array.isArray(data)) {
    console.warn('数据格式不正确')
    return []
  }

  // 递归处理数据，特别处理区县数据格式问题
  const processNode = (node: any, level: number = 0): Pca => {
    const processed: Pca = {
      value: node.value || node.label || node.name || '',
      label: node.label || node.value || node.name || '',
      children: undefined
    }

    // 处理子节点
    if (node.children && Array.isArray(node.children) && node.children.length > 0) {
      // 如果是城市级别（level=1），需要特殊处理区县数据
      if (level === 1) {
        const processedChildren: Pca[] = []

        node.children.forEach((child: any) => {
          const childValue = child.value || child.label || child.name || ''

          // 检查是否是合并的区县字符串（包含逗号）
          if (childValue.includes(',')) {
            console.log(`发现合并的区县数据: ${childValue}`)
            // 拆分区县字符串
            const districts = childValue.split(',').filter((district: string) => district.trim())

            districts.forEach((district: string) => {
              const trimmedDistrict = district.trim()
              if (trimmedDistrict) {
                processedChildren.push({
                  value: trimmedDistrict,
                  label: trimmedDistrict,
                  children: undefined
                })
              }
            })
          } else {
            // 正常的单个区县
            processedChildren.push(processNode(child, level + 1))
          }
        })

        processed.children = processedChildren
      } else {
        // 省份级别，正常处理
        processed.children = node.children.map((child: any) => processNode(child, level + 1))
      }
    }

    return processed
  }

  const result = data.map((province: any) => processNode(province, 0))
  console.log('处理后的数据:', result)

  // 验证修复效果
  if (result.length > 0 && result[0].children && result[0].children.length > 0) {
    const firstCity = result[0].children[0]
    if (firstCity.children) {
      console.log(`修复后第一个城市的区县数量: ${firstCity.children.length}`)
      console.log('修复后前5个区县:', firstCity.children.slice(0, 5))
    }
  }

  return result
}

// 加载省市区数据
const loadPcaData = async () => {
  loading.value = true
  try {
    console.log('开始加载省市区数据...')
    const response = await projectApi.queryPca()
    const pcaData = response.data

    console.log('省市区原始数据:', pcaData)

    if (pcaData && Array.isArray(pcaData) && pcaData.length > 0) {
      console.log('原始数据结构检查:')
      console.log('- 数据类型:', typeof pcaData)
      console.log('- 是否数组:', Array.isArray(pcaData))
      console.log('- 数据长度:', pcaData.length)
      console.log('- 第一个省份:', pcaData[0])
      if (pcaData[0] && pcaData[0].children) {
        console.log('- 第一个省份的城市数量:', pcaData[0].children.length)
        console.log('- 第一个城市:', pcaData[0].children[0])
        if (pcaData[0].children[0] && pcaData[0].children[0].children) {
          console.log('- 第一个城市的区县数量:', pcaData[0].children[0].children.length)
          console.log('- 第一个区县:', pcaData[0].children[0].children[0])
        }
      }

      // 处理数据
      pcaOptions.value = processData(pcaData)
      console.log('省市区处理后数据:', pcaOptions.value)

      // 验证处理后的数据结构
      if (pcaOptions.value.length > 0) {
        console.log('验证处理后数据:')
        console.log('- 省份数量:', pcaOptions.value.length)
        const firstProvince = pcaOptions.value[0]
        console.log('- 第一个省份:', firstProvince)
        if (firstProvince.children && firstProvince.children.length > 0) {
          console.log('- 第一个省份的城市数量:', firstProvince.children.length)
          const firstCity = firstProvince.children[0]
          console.log('- 第一个城市:', firstCity)
          if (firstCity.children && firstCity.children.length > 0) {
            console.log('- 第一个城市的区县数量:', firstCity.children.length)
            console.log('- 前3个区县:', firstCity.children.slice(0, 3))
          }
        }
      }

      // 如果有传入的完整地址，尝试解析
      if (props.fullAddress) {
        parseFullAddress(props.fullAddress)
      }
    } else {
      console.warn('省市区数据为空或格式不正确')
      ElMessage.warning('省市区数据为空，请检查后端接口')
      // 提供一些测试数据
      pcaOptions.value = [
        {
          value: '山东省',
          label: '山东省',
          children: [
            {
              value: '济南市',
              label: '济南市',
              children: [
                { value: '历下区', label: '历下区' },
                { value: '市中区', label: '市中区' },
                { value: '槐荫区', label: '槐荫区' }
              ]
            }
          ]
        }
      ]
    }
  } catch (error) {
    console.error('加载省市区数据失败:', error)
    ElMessage.error('加载省市区数据失败，使用测试数据')
    // 提供测试数据
    pcaOptions.value = [
      {
        value: '山东省',
        label: '山东省',
        children: [
          {
            value: '济南市',
            label: '济南市',
            children: [
              { value: '历下区', label: '历下区' },
              { value: '市中区', label: '市中区' },
              { value: '槐荫区', label: '槐荫区' }
            ]
          }
        ]
      },
      {
        value: '北京市',
        label: '北京市',
        children: [
          {
            value: '朝阳区',
            label: '朝阳区'
          },
          {
            value: '海淀区',
            label: '海淀区'
          }
        ]
      }
    ]
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadPcaData()
})
</script>

<style scoped>
.pca-cascader {
  width: 100%;
}

.el-cascader {
  width: 100%;
}

.selected-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.loading-text {
  font-size: 12px;
  color: #409eff;
  margin-top: 4px;
}

.debug-info {
  font-size: 12px;
  color: #67c23a;
  margin-top: 4px;
}
</style>
