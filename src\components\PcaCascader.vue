<template>
  <el-cascader
    v-model="selectedValues"
    :options="pcaOptions"
    :props="cascaderProps"
    placeholder="请选择省市区"
    clearable
    filterable
    @change="handleChange"
    style="width: 100%"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { projectApi, type Pca } from '@/api/project'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue?: string[]
  province?: string
  city?: string
  district?: string
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', province: string, city: string, district: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedValues = ref<string[]>([])
const pcaOptions = ref<any[]>([])

// 级联选择器配置
const cascaderProps = {
  value: 'name',
  label: 'name',
  children: 'children',
  emitPath: false, // 只返回最后一级的值
  checkStrictly: false // 严格模式，只能选择叶子节点
}

// 监听外部传入的值变化
watch(() => [props.province, props.city, props.district], ([province, city, district]) => {
  if (province && city && district) {
    selectedValues.value = [province, city, district]
  }
}, { immediate: true })

watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    selectedValues.value = [...newVal]
  }
}, { immediate: true })

// 处理选择变化
const handleChange = (value: string[]) => {
  selectedValues.value = value || []
  emit('update:modelValue', selectedValues.value)
  
  if (value && value.length === 3) {
    emit('change', value[0], value[1], value[2])
  } else {
    emit('change', '', '', '')
  }
}

// 构建树形结构
const buildTree = (data: Pca[]): any[] => {
  const map = new Map<number, any>()
  const result: any[] = []

  // 创建节点映射
  data.forEach(item => {
    map.set(item.id, {
      ...item,
      children: []
    })
  })

  // 构建树形结构
  data.forEach(item => {
    const node = map.get(item.id)
    if (item.parentId === 0) {
      // 根节点（省份）
      result.push(node)
    } else {
      // 子节点
      const parent = map.get(item.parentId)
      if (parent) {
        parent.children.push(node)
      }
    }
  })

  // 清理空的children数组
  const cleanEmptyChildren = (nodes: any[]) => {
    nodes.forEach(node => {
      if (node.children && node.children.length === 0) {
        delete node.children
      } else if (node.children) {
        cleanEmptyChildren(node.children)
      }
    })
  }

  cleanEmptyChildren(result)
  return result
}

// 加载省市区数据
const loadPcaData = async () => {
  try {
    const response = await projectApi.queryPca()
    const pcaData = response.data
    
    console.log('省市区原始数据:', pcaData)
    
    // 构建树形结构
    pcaOptions.value = buildTree(pcaData)
    
    console.log('省市区树形数据:', pcaOptions.value)
  } catch (error) {
    console.error('加载省市区数据失败:', error)
    ElMessage.error('加载省市区数据失败')
  }
}

onMounted(() => {
  loadPcaData()
})
</script>

<style scoped>
.el-cascader {
  width: 100%;
}
</style>
