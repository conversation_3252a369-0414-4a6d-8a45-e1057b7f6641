<script setup lang="ts">
import {getCurrentInstance ,onMounted, ref} from "vue";
import axios from "axios";
import defaultimage from '@/assets/logo.svg'
import router from "@/router";
const v3=getCurrentInstance()
const eturl=v3.appContext.config.globalProperties.$url
const queryParams=ref({
  pageNumber:1,
  pageSize:3,
  name:'',
  schid:'',
})
const start=ref(0)
const cxjs=(v:number)=>{
  return start.value+v+1
}
const total=ref(0)
const tableData=ref([])
const queryData=()=>{
  axios.post('/querySome',queryParams.value)
      .then(resp=>{
        tableData.value=resp.data.data.list
        total.value=resp.data.data.total
        start.value=resp.data.data.start
      })
}
const handleSizeChange=(v:number)=>{
  queryParams.value.pageSize=v
  queryParams.value.pageNumber=1
  queryData()
}
const handleCurrentChange=(v:number)=>{
  queryParams.value.pageNumber=v
  queryData()
}
onMounted(()=>{
  queryData()
  queryAllSchs()
})
const schs=ref<Array<any>>([])
const queryAllSchs=()=>{
  axios.get('/queryAllSchs').then(res=>{
    if(res.data.code==200){
      schs.value=res.data.data
    }
  })
}
const jumpToInfo=(sid:number)=>{
  router.push({
    path:'/stuinfo/'+sid
  })
}
</script>

<template>
  <el-card class="box-card">
    <el-form :inline="true" :model="queryParams" class="demo-form-inline">
      <el-form-item label="姓名">
        <el-input v-model="queryParams.name" placeholder="按名字查询" clearable />
      </el-form-item>
      <el-form-item label="所在学校">
        <el-select style="width:150px"
                   v-model="queryParams.schid"
                   placeholder="所在学校"
                   clearable
        >
          <el-option v-for="(item,index) in schs" label="item.name" value="item.id" />

        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border
              stripe style="width: 100%">
      <el-table-column type="expaand" />
      <el-table-column type="selection" />
      <el-table-column type="index" :index="cxjs" />
      <el-table-column prop="name" label="姓名"  />
      <el-table-column  label="头像" >
        <template #default="scope">
          <el-image :src="scope.row.tx?eturl+scope.row.tx.savapath:defaultimage"/>
        </template>
      </el-table-column>
      <el-table-column prop="age" label="年龄" />
      <el-table-column prop="birth" label="生日" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="phone" label="电话" />
      <el-table-column prop="sch.name" label="所在学校" />
      <el-table-column prop="sch.info" label="学校地址" />
      <el-table-column prop="sch.phone" label="学校电话" />
      <el-form-item label="操作" >
        <template #default="scope">
          <el-button type="primary" @click="jumpToInfo(scope.row.id)">学生详情</el-button>
          <el-button type="primary" @click="">选课详情</el-button>
        </template>
      </el-form-item>


    </el-table>
    <el-pagination v-model:current-page="queryParams.pageNumber"
                   v-model:page-size="queryParams.pageSize"
                   :page-sizes="[3,5,8,10]"
                   :disabled="false"
                   :background="true"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="total" @size-change="handleSizeChange"
                   @current-change="handleCurrentChange" />
  </el-card>
</template>

<style scoped>

</style>