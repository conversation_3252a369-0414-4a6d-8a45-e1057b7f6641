<template>
  <div class="ren-container">
    <!-- 页面标题和操作按钮 -->
    <div class="header">
      <h2>人员信息维护</h2>
      <el-button type="primary" @click="handleAdd" :icon="Plus">添加人员</el-button>
    </div>

    <!-- 人员列表表格 -->
    <el-table 
      :data="renList" 
      style="width: 100%" 
      v-loading="loading"
      element-loading-text="加载中..."
    >
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="name" label="姓名" width="100" />
      <el-table-column prop="age" label="年龄" width="80" />
      <el-table-column prop="birth" label="出生日期" width="120" />
      <el-table-column prop="phone" label="联系电话" width="130" />
      <el-table-column prop="front" label="前端技能" min-width="120" show-overflow-tooltip />
      <el-table-column prop="back" label="后端技能" min-width="120" show-overflow-tooltip />
      <el-table-column prop="dao" label="数据访问层" min-width="120" show-overflow-tooltip />
      <el-table-column prop="db" label="数据库技能" min-width="120" show-overflow-tooltip />
      <el-table-column prop="city" label="所在城市" width="100" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑人员对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form 
        :model="formData" 
        :rules="formRules" 
        ref="formRef" 
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formData.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄" prop="age">
              <el-input-number 
                v-model="formData.age" 
                :min="18" 
                :max="65" 
                placeholder="请输入年龄" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birth">
              <el-date-picker
                v-model="formData.birth"
                type="date"
                placeholder="请选择出生日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="所在城市" prop="city">
          <el-input v-model="formData.city" placeholder="请输入所在城市" />
        </el-form-item>

        <el-form-item label="前端技能" prop="front">
          <el-input 
            v-model="formData.front" 
            type="textarea" 
            :rows="2"
            placeholder="请输入前端技能，如：Vue3, React, TypeScript等"
          />
        </el-form-item>

        <el-form-item label="后端技能" prop="back">
          <el-input 
            v-model="formData.back" 
            type="textarea" 
            :rows="2"
            placeholder="请输入后端技能，如：Spring Boot, Node.js等"
          />
        </el-form-item>

        <el-form-item label="数据访问层" prop="dao">
          <el-input 
            v-model="formData.dao" 
            type="textarea" 
            :rows="2"
            placeholder="请输入数据访问层技能，如：MyBatis, JPA等"
          />
        </el-form-item>

        <el-form-item label="数据库技能" prop="db">
          <el-input 
            v-model="formData.db" 
            type="textarea" 
            :rows="2"
            placeholder="请输入数据库技能，如：MySQL, PostgreSQL等"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { renService, type Ren } from '@/api/ren'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const renList = ref<Ren[]>([])
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<Ren>({
  name: '',
  age: 25,
  birth: '',
  phone: '',
  front: '',
  back: '',
  dao: '',
  db: '',
  city: ''
})

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑人员' : '添加人员')

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', min: 18, max: 65, message: '年龄必须在 18 到 65 之间', trigger: 'blur' }
  ],
  birth: [
    { required: true, message: '请选择出生日期', trigger: 'change' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  city: [
    { required: true, message: '请输入所在城市', trigger: 'blur' }
  ],
  front: [
    { required: true, message: '请输入前端技能', trigger: 'blur' }
  ],
  back: [
    { required: true, message: '请输入后端技能', trigger: 'blur' }
  ]
}

// 页面方法
/**
 * 获取人员列表
 */
const getRenList = async () => {
  try {
    loading.value = true
    renList.value = await renService.getAllRens()
    console.log('获取到人员列表:', renList.value)
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 重置表单数据
 */
const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    age: 25,
    birth: '',
    phone: '',
    front: '',
    back: '',
    dao: '',
    db: '',
    city: ''
  })
}

/**
 * 处理添加人员
 */
const handleAdd = () => {
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

/**
 * 处理编辑人员
 */
const handleEdit = (row: Ren) => {
  isEdit.value = true
  Object.assign(formData, { ...row })
  dialogVisible.value = true
}

/**
 * 处理删除人员
 */
const handleDelete = async (row: Ren) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除人员 "${row.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 注意：后端没有提供删除接口，这里只是前端模拟
    // 实际项目中需要后端提供删除接口
    ElMessage.warning('后端暂未提供删除接口，请联系开发人员添加删除功能')

  } catch (error) {
    console.log('取消删除')
  }
}

/**
 * 处理对话框关闭
 */
const handleDialogClose = () => {
  formRef.value?.resetFields()
  dialogVisible.value = false
}

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEdit.value) {
      // 编辑模式 - 后端暂未提供更新接口
      ElMessage.warning('后端暂未提供更新接口，请联系开发人员添加更新功能')
    } else {
      // 添加模式
      const result = await renService.addRen(formData)
      if (result > 0) {
        ElMessage.success('添加人员成功')
        handleDialogClose()
        await getRenList() // 重新获取列表
      } else {
        ElMessage.error('添加人员失败')
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 页面挂载时获取数据
onMounted(() => {
  getRenList()
})
</script>

<style scoped>
.ren-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 6px;
}
</style>
