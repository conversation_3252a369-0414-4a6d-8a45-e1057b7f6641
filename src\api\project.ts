import axios from 'axios'

// 项目接口类型定义（匹配后端实体类字段）
export interface Project {
  id?: number
  name: string           // 对应后端的 name 字段
  province: string       // 对应后端的 province 字段
  adress: string         // 对应后端的 adress 字段（注意拼写）
  xuqiu: string          // 对应后端的 xuqiu 字段
  startTime: string
  endTime: string
  nums: number           // 对应后端的 nums 字段
  salary: number         // 对应后端的 salary 字段
  fuzeren: string        // 对应后端的 fuzeren 字段
}

// 分页参数接口
export interface PageParams {
  page?: number
  size?: number
}

// 搜索参数接口
export interface SearchParams extends PageParams {
  projectName?: string
  city?: string
  manager?: string
}

// 后端响应数据结构
export interface ApiResponse<T> {
  code?: number
  message?: string
  data: T
  total?: number
}

// 项目API服务
export const projectApi = {
  /**
   * 添加项目
   * @param project 项目信息
   * @returns Promise<number> 返回影响的行数
   */
  addProject: (project: Project) => {
    return axios.post<number>('/pro/addProject', project)
  },

  /**
   * 获取所有项目列表
   * @returns Promise<Project[]> 项目列表
   */
  getAllProjects: () => {
    return axios.get<Project[]>('/pro/getAllProjects')
  },

  /**
   * 更新项目信息
   * @param project 项目信息
   * @returns Promise<number> 返回影响的行数
   */
  updateProject: (project: Project) => {
    return axios.post<number>('/pro/updateProject', project)
  },

  /**
   * 删除项目
   * @param id 项目ID
   * @returns Promise<number> 返回影响的行数
   */
  deleteProject: (id: number) => {
    return axios.post<number>('/pro/deleteProject', { id })
  },

  /**
   * 搜索项目
   * @param searchParams 搜索参数
   * @returns Promise<Project[]> 项目列表
   */
  searchProjects: (searchParams: SearchParams) => {
    return axios.get<ApiResponse<Project[]>>('/pro/searchProjects', { params: searchParams })
  },

  /**
   * 根据ID获取项目详情
   * @param id 项目ID
   * @returns Promise<Project> 项目详情
   */
  getProjectById: (id: number) => {
    return axios.get<Project>(`/pro/getProject/${id}`)
  }
}

// 导出默认的项目API
export default projectApi
