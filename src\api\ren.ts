import axios from 'axios'

// 人员数据接口
export interface Ren {
  id?: number
  name: string
  age: number
  birth: string // 日期字符串格式
  phone: string
  front: string // 前端技能
  back: string // 后端技能
  dao: string // 数据访问层技能
  db: string // 数据库技能
  city: string // 注意：后端实体类中是 City（大写C）
}

// 删除人员DTO接口
export interface DeleteRenDto {
  id: number
}

// 人员API服务类
class RenService {
  private baseURL = '/ren'

  /**
   * 获取所有人员列表
   */
  async getAllRens(): Promise<Ren[]> {
    try {
      console.log('=== 获取人员列表 ===')
      const response = await axios.get(`${this.baseURL}/getAllRens`)
      console.log('人员列表响应:', response.data)
      return response.data || []
    } catch (error) {
      console.error('获取人员列表失败:', error)
      throw new Error('获取人员列表失败')
    }
  }

  /**
   * 添加新人员
   */
  async addRen(ren: Ren): Promise<number> {
    try {
      console.log('=== 添加人员 ===')
      console.log('添加人员数据:', ren)
      
      // 处理数据格式，确保字段名匹配后端
      const renData = {
        name: ren.name,
        age: ren.age,
        birth: ren.birth,
        phone: ren.phone,
        front: ren.front,
        back: ren.back,
        dao: ren.dao,
        db: ren.db,
        city: ren.city // 后端实体类中是 City，但这里用小写传递
      }
      
      const response = await axios.post(`${this.baseURL}/addRen`, renData)
      console.log('添加人员响应:', response.data)
      return response.data
    } catch (error) {
      console.error('添加人员失败:', error)
      throw new Error('添加人员失败')
    }
  }

  /**
   * 删除人员
   */
  async deleteRen(id: number): Promise<number> {
    try {
      console.log('=== 删除人员 ===')
      console.log('删除人员ID:', id)

      const deleteDto: DeleteRenDto = { id }
      const response = await axios.post(`${this.baseURL}/deleteRen`, deleteDto)
      console.log('删除人员响应:', response.data)
      return response.data
    } catch (error) {
      console.error('删除人员失败:', error)
      throw new Error('删除人员失败')
    }
  }

  /**
   * 更新人员信息
   * 注意：此方法需要后端提供对应的接口
   */
  async updateRen(ren: Ren): Promise<number> {
    try {
      console.log('=== 更新人员 ===')
      console.log('更新人员数据:', ren)

      // 处理数据格式，确保字段名匹配后端
      const renData = {
        id: ren.id,
        name: ren.name,
        age: ren.age,
        birth: ren.birth,
        phone: ren.phone,
        front: ren.front,
        back: ren.back,
        dao: ren.dao,
        db: ren.db,
        city: ren.city
      }

      const response = await axios.post(`${this.baseURL}/updateRen`, renData)
      console.log('更新人员响应:', response.data)
      return response.data
    } catch (error) {
      console.error('更新人员失败:', error)
      throw new Error('更新人员失败')
    }
  }
}

// 导出服务实例
export const renService = new RenService()

// 导出默认服务
export default renService
