<template>
  <div class="simple-test">
    <el-card>
      <h2>简单测试页面</h2>
      
      <h3>1. 测试级联选择器组件</h3>
      <div style="width: 300px; margin: 20px 0;">
        <PcaCascader 
          v-model="testRegion"
          @change="handleTestChange"
        />
      </div>
      <p>选择结果：{{ testResult }}</p>
      
      <h3>2. 测试日期选择器</h3>
      <div style="width: 200px; margin: 20px 0;">
        <el-date-picker
          v-model="testDate"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :teleported="false"
          @change="handleDateChange"
        />
      </div>
      <p>选择的日期：{{ testDate }}</p>
      <p>格式化显示：{{ formatDate(testDate) }}</p>
      
      <h3>3. 测试后端连接</h3>
      <el-button @click="testBackend" :loading="loading">测试后端连接</el-button>
      <p>{{ backendResult }}</p>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import PcaCascader from '@/components/PcaCascader.vue'
import { projectApi } from '@/api/project'

const testRegion = ref<string[]>([])
const testResult = ref('')
const testDate = ref('')
const loading = ref(false)
const backendResult = ref('')

const handleTestChange = (fullRegion: string, province: string, city: string, district: string) => {
  testResult.value = `完整地区：${fullRegion}，省：${province}，市：${city}，区：${district}`
  console.log('级联选择器变化:', { fullRegion, province, city, district })
}

const handleDateChange = (value: string) => {
  console.log('日期选择变化:', value)
  ElMessage.info(`选择的日期：${value}`)
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '未选择'
  const date = new Date(dateStr)
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

const testBackend = async () => {
  loading.value = true
  backendResult.value = '测试中...'
  
  try {
    // 测试获取项目列表
    const response = await projectApi.getAllProjects()
    backendResult.value = `后端连接成功！获取到 ${response.data.length} 个项目`
    ElMessage.success('后端连接测试成功')
  } catch (error: any) {
    backendResult.value = `后端连接失败：${error.message}`
    ElMessage.error('后端连接测试失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

h3 {
  color: #409eff;
  margin-top: 30px;
  margin-bottom: 15px;
}

p {
  margin: 10px 0;
  color: #606266;
}
</style>
