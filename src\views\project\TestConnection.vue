<template>
  <div class="test-container">
    <h3>后端连接测试</h3>
    
    <el-card>
      <h4>连接信息</h4>
      <p><strong>Base URL:</strong> {{ baseURL }}</p>
      <p><strong>项目API URL:</strong> {{ baseURL }}/pro/addProject</p>
      
      <el-button type="primary" @click="testConnection" :loading="testing">
        测试连接
      </el-button>
      
      <div v-if="testResult" class="test-result">
        <h4>测试结果:</h4>
        <pre>{{ testResult }}</pre>
      </div>
    </el-card>
    
    <el-card style="margin-top: 20px;">
      <h4>测试添加项目</h4>
      <el-button type="success" @click="testAddProject" :loading="testing">
        测试添加项目
      </el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { projectApi } from '@/api/project'

const testing = ref(false)
const testResult = ref('')
const baseURL = ref(axios.defaults.baseURL)

const testConnection = async () => {
  testing.value = true
  testResult.value = ''
  
  try {
    // 测试基本连接
    const response = await axios.get('/pro/test', { timeout: 5000 })
    testResult.value = `连接成功!\n响应: ${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('连接测试成功')
  } catch (error: any) {
    testResult.value = `连接失败!\n错误: ${error.message}\n详情: ${JSON.stringify(error.response?.data || {}, null, 2)}`
    ElMessage.error('连接测试失败')
    console.error('连接测试失败:', error)
  } finally {
    testing.value = false
  }
}

const testAddProject = async () => {
  testing.value = true
  testResult.value = ''
  
  try {
    const testProject = {
      projectName: '测试项目',
      city: '济南',
      address: '山东省-济南市-历下区-测试地址',
      techRequirement: 'Vue3 + Spring Boot',
      startTime: '2024-01-01',
      endTime: '2024-12-31',
      requiredPersons: 3,
      price: 30000,
      manager: '测试负责人'
    }
    
    console.log('发送测试项目数据:', testProject)
    const response = await projectApi.addProject(testProject)
    
    testResult.value = `添加项目测试成功!\n响应: ${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('添加项目测试成功')
  } catch (error: any) {
    testResult.value = `添加项目测试失败!\n错误: ${error.message}\n详情: ${JSON.stringify(error.response?.data || {}, null, 2)}`
    ElMessage.error('添加项目测试失败')
    console.error('添加项目测试失败:', error)
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.test-result {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.test-result pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
