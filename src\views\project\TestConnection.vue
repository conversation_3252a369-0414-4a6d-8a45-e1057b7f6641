<template>
  <div class="test-container">
    <h3>后端连接测试</h3>
    
    <el-card>
      <h4>连接信息</h4>
      <p><strong>Base URL:</strong> {{ baseURL }}</p>
      <p><strong>项目API URL:</strong> {{ baseURL }}/pro/addProject</p>
      
      <el-button type="primary" @click="testConnection" :loading="testing">
        测试连接
      </el-button>
      
      <div v-if="testResult" class="test-result">
        <h4>测试结果:</h4>
        <pre>{{ testResult }}</pre>
      </div>
    </el-card>
    
    <el-card style="margin-top: 20px;">
      <h4>测试获取项目列表</h4>
      <el-button type="info" @click="testGetAllProjects" :loading="testing">
        测试 getAllProjects
      </el-button>
    </el-card>

    <el-card style="margin-top: 20px;">
      <h4>测试获取省市区数据</h4>
      <el-button type="info" @click="testQueryPca" :loading="testing">
        测试获取省市区
      </el-button>
      <el-button type="warning" @click="testPcaEndpoint" :loading="testing" style="margin-left: 10px;">
        直接测试接口
      </el-button>
    </el-card>

    <el-card style="margin-top: 20px;">
      <h4>测试添加项目</h4>
      <el-button type="success" @click="testAddProject" :loading="testing">
        测试添加项目
      </el-button>
      <el-button type="warning" @click="testSimpleProject" :loading="testing" style="margin-left: 10px;">
        测试简化项目
      </el-button>
    </el-card>

    <el-card style="margin-top: 20px;">
      <h4>测试修改项目</h4>
      <el-input v-model="testProjectId" placeholder="请输入要修改的项目ID" style="width: 200px; margin-right: 10px;" />
      <el-button type="primary" @click="testUpdateProject" :loading="testing">
        测试修改项目
      </el-button>
    </el-card>

    <el-card style="margin-top: 20px;">
      <h4>测试删除项目</h4>
      <el-input v-model="deleteProjectId" placeholder="请输入要删除的项目ID" style="width: 200px; margin-right: 10px;" />
      <el-button type="danger" @click="testDeleteProject" :loading="testing">
        测试删除项目
      </el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { projectApi } from '@/api/project'

const testing = ref(false)
const testResult = ref('')
const baseURL = ref(axios.defaults.baseURL)
const testProjectId = ref('')
const deleteProjectId = ref('')

const testConnection = async () => {
  testing.value = true
  testResult.value = ''

  try {
    // 测试基本连接 - 尝试访问项目Controller
    const response = await axios.get('/pro/test', { timeout: 5000 })
    testResult.value = `连接成功!\n响应: ${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('连接测试成功')
  } catch (error: any) {
    // 如果/pro/test不存在，尝试直接测试添加接口
    testResult.value = `连接测试失败，但这是正常的，因为/pro/test接口可能不存在。\n错误: ${error.message}\n状态码: ${error.response?.status}\n请直接测试添加项目功能。`
    ElMessage.warning('测试接口不存在，请直接测试添加项目')
    console.error('连接测试失败:', error)
  } finally {
    testing.value = false
  }
}

const testGetAllProjects = async () => {
  testing.value = true
  testResult.value = ''

  try {
    console.log('=== 测试获取项目列表 ===')
    console.log('请求URL:', `${axios.defaults.baseURL}/pro/getAllProjects`)
    console.log('请求方法: GET')

    const response = await projectApi.getAllProjects()

    console.log('响应成功:', response)
    testResult.value = `获取项目列表成功!\n项目数量: ${Array.isArray(response.data) ? response.data.length : 0}\n响应数据: ${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('获取项目列表测试成功')
  } catch (error: any) {
    console.error('=== 获取项目列表失败 ===')
    console.error('完整错误对象:', error)
    console.error('错误消息:', error.message)
    console.error('错误代码:', error.code)
    console.error('响应状态:', error.response?.status)
    console.error('响应状态文本:', error.response?.statusText)
    console.error('响应数据:', error.response?.data)
    console.error('请求配置:', error.config)
    console.error('========================')

    let errorDetails = `获取项目列表失败!\n`
    errorDetails += `错误: ${error.message}\n`
    errorDetails += `状态码: ${error.response?.status || 'N/A'}\n`
    errorDetails += `状态文本: ${error.response?.statusText || 'N/A'}\n`
    errorDetails += `响应数据: ${JSON.stringify(error.response?.data || {}, null, 2)}\n`
    errorDetails += `请求URL: ${error.config?.url || 'N/A'}\n`

    testResult.value = errorDetails
    ElMessage.error('获取项目列表测试失败')
  } finally {
    testing.value = false
  }
}

const testQueryPca = async () => {
  testing.value = true
  testResult.value = ''

  try {
    console.log('=== 测试获取省市区数据 ===')
    console.log('请求URL:', `${axios.defaults.baseURL}/pro/queryPca`)
    console.log('请求方法: GET')

    // 先直接用 axios 测试
    const directResponse = await axios.get('/pro/queryPca', { timeout: 10000 })
    console.log('直接请求响应:', directResponse)

    // 再用 API 封装测试
    const response = await projectApi.queryPca()

    console.log('省市区数据响应:', response)
    testResult.value = `获取省市区数据成功!\n数据数量: ${Array.isArray(response.data) ? response.data.length : 0}\n数据预览: ${JSON.stringify(response.data.slice(0, 2), null, 2)}`
    ElMessage.success('获取省市区数据成功')
  } catch (error: any) {
    console.error('=== 获取省市区数据失败 ===')
    console.error('完整错误对象:', error)
    console.error('错误消息:', error.message)
    console.error('错误代码:', error.code)
    console.error('响应状态:', error.response?.status)
    console.error('响应状态文本:', error.response?.statusText)
    console.error('响应数据:', error.response?.data)
    console.error('请求配置:', error.config)
    console.error('========================')

    let errorDetails = `获取省市区数据失败!\n`
    errorDetails += `错误类型: ${error.name || 'Unknown'}\n`
    errorDetails += `错误消息: ${error.message}\n`
    errorDetails += `错误代码: ${error.code || 'N/A'}\n`

    if (error.response) {
      errorDetails += `\n=== 服务器响应 ===\n`
      errorDetails += `状态码: ${error.response.status}\n`
      errorDetails += `状态文本: ${error.response.statusText}\n`
      errorDetails += `响应数据: ${JSON.stringify(error.response.data, null, 2)}\n`
    } else if (error.request) {
      errorDetails += `\n=== 网络请求 ===\n`
      errorDetails += `请求已发送但未收到响应\n`
      errorDetails += `可能是网络问题或服务器未启动\n`
    }

    if (error.config) {
      errorDetails += `\n=== 请求配置 ===\n`
      errorDetails += `URL: ${error.config.url}\n`
      errorDetails += `方法: ${error.config.method}\n`
      errorDetails += `基础URL: ${error.config.baseURL}\n`
    }

    testResult.value = errorDetails
    ElMessage.error('获取省市区数据失败')
  } finally {
    testing.value = false
  }
}

const testPcaEndpoint = async () => {
  testing.value = true
  testResult.value = ''

  try {
    console.log('=== 直接测试 queryPca 接口 ===')
    console.log('完整URL:', `${axios.defaults.baseURL}/pro/queryPca`)

    // 使用最基本的 axios 请求
    const response = await axios({
      method: 'GET',
      url: '/pro/queryPca',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    console.log('接口响应成功:', response)
    testResult.value = `直接接口测试成功!\n状态码: ${response.status}\n数据类型: ${typeof response.data}\n数据数量: ${Array.isArray(response.data) ? response.data.length : 'N/A'}\n数据预览: ${JSON.stringify(response.data, null, 2).substring(0, 500)}...`
    ElMessage.success('直接接口测试成功')
  } catch (error: any) {
    console.error('=== 直接接口测试失败 ===')
    console.error('错误对象:', error)

    let errorDetails = `直接接口测试失败!\n`
    errorDetails += `错误: ${error.message}\n`

    if (error.response) {
      errorDetails += `状态码: ${error.response.status}\n`
      errorDetails += `状态文本: ${error.response.statusText}\n`
      errorDetails += `响应数据: ${JSON.stringify(error.response.data, null, 2)}\n`
    } else if (error.request) {
      errorDetails += `请求已发送但未收到响应\n`
      errorDetails += `可能原因: 后端服务未启动或接口不存在\n`
    } else {
      errorDetails += `请求配置错误: ${error.message}\n`
    }

    errorDetails += `\n请检查:\n`
    errorDetails += `1. 后端服务是否在 8080 端口启动\n`
    errorDetails += `2. Controller 中是否有 @GetMapping("/queryPca") 接口\n`
    errorDetails += `3. 接口路径是否正确: /pro/queryPca\n`

    testResult.value = errorDetails
    ElMessage.error('直接接口测试失败')
  } finally {
    testing.value = false
  }
}

const testAddProject = async () => {
  testing.value = true
  testResult.value = ''

  try {
    const testProject = {
      name: '测试项目',
      province: '山东省',
      adress: '山东省济南市历下区-趵北路6号-测试详细地址',
      xuqiu: 'Vue3 + Spring Boot',
      startTime: '2024-01-01',
      endTime: '2024-12-31',
      nums: 3,
      salary: 30000,
      fuzeren: '测试负责人'
    }

    console.log('=== 发送测试项目数据 ===')
    console.log('请求URL:', `${axios.defaults.baseURL}/pro/addProject`)
    console.log('请求方法: POST')
    console.log('请求头:', {
      'Content-Type': 'application/json'
    })
    console.log('请求数据:', JSON.stringify(testProject, null, 2))
    console.log('========================')

    const response = await projectApi.addProject(testProject)

    testResult.value = `添加项目测试成功!\n响应状态: ${response.status}\n响应数据: ${JSON.stringify(response.data, null, 2)}\n完整响应: ${JSON.stringify(response, null, 2)}`
    ElMessage.success('添加项目测试成功')
  } catch (error: any) {
    console.error('=== 添加项目测试失败 ===')
    console.error('完整错误对象:', error)
    console.error('错误消息:', error.message)
    console.error('错误代码:', error.code)
    console.error('响应状态:', error.response?.status)
    console.error('响应状态文本:', error.response?.statusText)
    console.error('响应头:', error.response?.headers)
    console.error('响应数据:', error.response?.data)
    console.error('请求配置:', error.config)
    console.error('请求URL:', error.config?.url)
    console.error('请求方法:', error.config?.method)
    console.error('请求头:', error.config?.headers)
    console.error('请求数据:', error.config?.data)
    console.error('========================')

    let errorDetails = `添加项目测试失败!\n`
    errorDetails += `错误类型: ${error.name || 'Unknown'}\n`
    errorDetails += `错误消息: ${error.message}\n`
    errorDetails += `错误代码: ${error.code || 'N/A'}\n`

    if (error.response) {
      errorDetails += `\n=== 服务器响应 ===\n`
      errorDetails += `状态码: ${error.response.status}\n`
      errorDetails += `状态文本: ${error.response.statusText}\n`
      errorDetails += `响应数据: ${JSON.stringify(error.response.data, null, 2)}\n`
    } else if (error.request) {
      errorDetails += `\n=== 网络请求 ===\n`
      errorDetails += `请求已发送但未收到响应\n`
      errorDetails += `可能是网络问题或服务器未启动\n`
    } else {
      errorDetails += `\n=== 请求配置错误 ===\n`
      errorDetails += `请求配置有误\n`
    }

    if (error.config) {
      errorDetails += `\n=== 请求配置 ===\n`
      errorDetails += `URL: ${error.config.url}\n`
      errorDetails += `方法: ${error.config.method}\n`
      errorDetails += `基础URL: ${error.config.baseURL}\n`
      errorDetails += `请求数据: ${JSON.stringify(error.config.data, null, 2)}\n`
    }

    testResult.value = errorDetails
    ElMessage.error('添加项目测试失败')
  } finally {
    testing.value = false
  }
}

const testSimpleProject = async () => {
  testing.value = true
  testResult.value = ''

  try {
    // 使用最简单的数据测试
    const simpleProject = {
      name: '简单测试',
      province: '北京市',
      adress: '北京市朝阳区-测试详细地址',
      xuqiu: '测试需求',
      startTime: '2024-01-01',
      endTime: '2024-12-31',
      nums: 1,
      salary: 1000,
      fuzeren: '测试人'
    }

    console.log('=== 发送简化测试数据 ===')
    console.log('数据:', JSON.stringify(simpleProject, null, 2))

    // 直接使用 axios 发送，不通过 API 封装
    const response = await axios.post('/pro/addProject', simpleProject, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    })

    testResult.value = `简化测试成功!\n响应: ${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('简化测试成功')
  } catch (error: any) {
    console.error('=== 简化测试失败 ===')
    console.error('错误:', error)

    testResult.value = `简化测试失败!\n错误: ${error.message}\n状态码: ${error.response?.status}\n响应: ${JSON.stringify(error.response?.data || {}, null, 2)}`
    ElMessage.error('简化测试失败')
  } finally {
    testing.value = false
  }
}

const testUpdateProject = async () => {
  testing.value = true
  testResult.value = ''

  if (!testProjectId.value) {
    ElMessage.error('请输入项目ID')
    testing.value = false
    return
  }

  try {
    const updateProject = {
      id: parseInt(testProjectId.value),
      name: '修改后的项目名称',
      province: '上海市',
      adress: '上海市浦东新区-修改后的详细地址',
      xuqiu: '修改后的技术需求',
      startTime: '2024-02-01',
      endTime: '2024-11-30',
      nums: 5,
      salary: 60000,
      fuzeren: '修改后的负责人'
    }

    console.log('=== 测试修改项目 ===')
    console.log('项目ID:', testProjectId.value)
    console.log('修改数据:', JSON.stringify(updateProject, null, 2))

    const response = await projectApi.updateProject(updateProject)

    testResult.value = `修改项目测试成功!\n项目ID: ${testProjectId.value}\n响应: ${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('修改项目测试成功')
  } catch (error: any) {
    console.error('=== 修改项目测试失败 ===')
    console.error('错误:', error)

    testResult.value = `修改项目测试失败!\n项目ID: ${testProjectId.value}\n错误: ${error.message}\n状态码: ${error.response?.status}\n响应: ${JSON.stringify(error.response?.data || {}, null, 2)}`
    ElMessage.error('修改项目测试失败')
  } finally {
    testing.value = false
  }
}

const testDeleteProject = async () => {
  testing.value = true
  testResult.value = ''

  if (!deleteProjectId.value) {
    ElMessage.error('请输入项目ID')
    testing.value = false
    return
  }

  try {
    console.log('=== 测试删除项目 ===')
    console.log('项目ID:', deleteProjectId.value)

    const response = await projectApi.deleteProject(parseInt(deleteProjectId.value))

    testResult.value = `删除项目测试成功!\n项目ID: ${deleteProjectId.value}\n响应: ${JSON.stringify(response.data, null, 2)}`
    ElMessage.success('删除项目测试成功')
  } catch (error: any) {
    console.error('=== 删除项目测试失败 ===')
    console.error('错误:', error)

    testResult.value = `删除项目测试失败!\n项目ID: ${deleteProjectId.value}\n错误: ${error.message}\n状态码: ${error.response?.status}\n响应: ${JSON.stringify(error.response?.data || {}, null, 2)}`
    ElMessage.error('删除项目测试失败')
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.test-result {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.test-result pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
