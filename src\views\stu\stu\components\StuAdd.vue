<template>
    <el-form
            ref="ruleFormRef"
            style="max-width: 500px"
            :model="stuForm"
            :rules="rules"
            label-width="auto"
            class="demo-ruleForm"
            :size="formSize"
            status-icon
    >
        <el-form-item label="学生姓名" prop="name">
            <el-input v-model="stuForm.name" />
        </el-form-item>
        <el-form-item label="年龄" prop="age">
        <el-input-number v-model="stuForm.age" />
        </el-form-item>
        <el-form-item label="生日" prop="birth">
            <el-date-picker
                v-model="stuForm.birth"
                type="date"
                placeholder="选择学生生日"
                value-format="YYYY-MM-DD"
            />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
            <el-input v-model="stuForm.email" />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
            <el-input v-model="stuForm.phone" />
        </el-form-item>
        <el-form-item label="所在学校" prop="region">
            <el-select v-model="stuForm.schid" placeholder="请选择所在学校">
                <el-option v-for="item in schs" :label="item.name" :value="item.id" />
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm(ruleFormRef)">添加</el-button>
          <el-button type="success" @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>

        <el-form-item label="学生头像">
            <el-upload
                    v-model:file-list="fileList"
                    :action="eturl+'/addPic'"
                    list-type="picture-card"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :on-success="handleSuccess"
                    name="pic"
            >
                <el-icon><Plus /></el-icon>
            </el-upload>

            <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="Preview Image" />
            </el-dialog>
        </el-form-item>
    </el-form>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import {ElMessage, type UploadProps, type UploadUserFile} from 'element-plus'
import {getCurrentInstance, reactive, ref} from 'vue'
import type { ComponentSize, FormInstance, FormRules,UploadFile,UploadFiles } from 'element-plus'
import axios from "axios";

let v3 = getCurrentInstance()
const eturl = v3.appContext.config.globalProperties.$url
interface Student{
    name?: string
    age?: number
    birth?: Date | string
    email?: string
    phone?: string
    schid?: number
    pics?: string[]
}
const queryAllSchs = () =>{
  axios.get('/queryAllSchs').then(resp => {
    if(resp.data.code==200)
      return resp.data
  })
}
//所有的学校List
const schs = ref<Array<any>>()
const formSize = ref<ComponentSize>('default')
const ruleFormRef = ref<FormInstance>()
const stuForm = ref<Student>({
    name: '',
    age:0,
    birth:'',
    email:'',
    phone:'',
    schid:0,
    pics:[]
})
const rules = reactive<FormRules<Student>>({

})

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            axios.post('/addStu',stuForm.value).then(resp => {
              if(resp.data.code==200){
                ElMessage({
                  type: 'success',
                  message:"添加成功!"
                })
                stuForm.value.name = ''
                stuForm.value.age = 0
                stuForm.value.birth = ''
                stuForm.value.pics=[]
                fileList.value=[]
              }
            })
        } else {
            console.log('error submit!', fields)
        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
}


const fileList = ref<UploadUserFile[]>([

])

const dialogImageUrl = ref('')
const dialogVisible = ref(false)

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
    console.log(uploadFile, uploadFiles)
}

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
    dialogImageUrl.value = uploadFile.url!
    dialogVisible.value = true
}
//文件上传成功的钩子函数
const handleSuccess = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles)=>{
  if(response.code==200){
    //文件的保存的信息放到pic中
    stuForm.value.pics.push(response.data)
  }
}
</script>

<style scoped>

</style>