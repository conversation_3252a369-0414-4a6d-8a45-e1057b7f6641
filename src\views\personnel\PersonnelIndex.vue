<template>
  <div class="personnel-container">
    <div class="header-section">
      <h2>人员信息维护</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加人员
      </el-button>
    </div>

    <!-- 查询区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item label="技能">
          <el-input v-model="searchForm.skills" placeholder="请输入技能" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="空闲" value="free" />
            <el-option label="忙碌" value="busy" />
            <el-option label="休假" value="vacation" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 人员列表表格 -->
    <div class="table-section">
      <el-table :data="personnelList" border style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="phone" label="联系电话" width="140" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="skills" label="技能" min-width="200" show-overflow-tooltip />
        <el-table-column prop="experience" label="工作经验" width="100" />
        <el-table-column prop="level" label="级别" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="joinDate" label="入职时间" width="120" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formData.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        
        <el-form-item label="技能" prop="skills">
          <el-input 
            v-model="formData.skills" 
            placeholder="请输入技能，多个技能用逗号分隔"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工作经验" prop="experience">
              <el-input-number 
                v-model="formData.experience" 
                :min="0" 
                :max="50"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="级别" prop="level">
              <el-select v-model="formData.level" placeholder="请选择级别" style="width: 100%">
                <el-option label="初级" value="junior" />
                <el-option label="中级" value="middle" />
                <el-option label="高级" value="senior" />
                <el-option label="专家" value="expert" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="空闲" value="free" />
                <el-option label="忙碌" value="busy" />
                <el-option label="休假" value="vacation" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入职时间" prop="joinDate">
              <el-date-picker
                v-model="formData.joinDate"
                type="date"
                placeholder="选择入职时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 接口类型定义
interface Personnel {
  id?: number
  name: string
  phone: string
  email: string
  skills: string
  experience: number
  level: string
  status: string
  joinDate: string
}

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('添加人员')
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  skills: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 人员列表
const personnelList = ref<Personnel[]>([])

// 表单数据
const formData = reactive<Personnel>({
  name: '',
  phone: '',
  email: '',
  skills: '',
  experience: 0,
  level: '',
  status: '',
  joinDate: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  skills: [
    { required: true, message: '请输入技能', trigger: 'blur' }
  ],
  experience: [
    { required: true, message: '请输入工作经验', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择级别', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  joinDate: [
    { required: true, message: '请选择入职时间', trigger: 'change' }
  ]
}

// 模拟数据
const mockData: Personnel[] = [
  {
    id: 1,
    name: '张三',
    phone: '13812345678',
    email: '<EMAIL>',
    skills: 'Vue.js, TypeScript, Node.js, MySQL',
    experience: 5,
    level: 'senior',
    status: 'free',
    joinDate: '2020-03-15'
  },
  {
    id: 2,
    name: '李四',
    phone: '13987654321',
    email: '<EMAIL>',
    skills: 'React, Java, Spring Boot, Redis',
    experience: 3,
    level: 'middle',
    status: 'busy',
    joinDate: '2021-06-20'
  }
]

// 方法定义
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    free: 'success',
    busy: 'warning',
    vacation: 'info'
  }
  return statusMap[status] || ''
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    free: '空闲',
    busy: '忙碌',
    vacation: '休假'
  }
  return statusMap[status] || status
}

const loadPersonnelList = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    personnelList.value = mockData
    pagination.total = mockData.length
    loading.value = false
  }, 500)
}

const handleSearch = () => {
  // 实现搜索逻辑
  loadPersonnelList()
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.skills = ''
  searchForm.status = ''
  loadPersonnelList()
}

const handleAdd = () => {
  dialogTitle.value = '添加人员'
  resetFormData()
  dialogVisible.value = true
}

const handleEdit = (row: Personnel) => {
  dialogTitle.value = '编辑人员'
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = (row: Personnel) => {
  ElMessageBox.confirm(
    `确定要删除人员"${row.name}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 实现删除逻辑
    ElMessage.success('删除成功')
    loadPersonnelList()
  })
}

const handleSubmit = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      // 实现提交逻辑
      ElMessage.success(dialogTitle.value === '添加人员' ? '添加成功' : '修改成功')
      dialogVisible.value = false
      loadPersonnelList()
    }
  })
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
}

const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    phone: '',
    email: '',
    skills: '',
    experience: 0,
    level: '',
    status: '',
    joinDate: ''
  })
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadPersonnelList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadPersonnelList()
}

// 生命周期
onMounted(() => {
  loadPersonnelList()
})
</script>

<style scoped>
.personnel-container {
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-section {
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
