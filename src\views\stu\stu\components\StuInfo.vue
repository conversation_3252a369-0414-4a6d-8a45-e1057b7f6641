<script setup lang="ts">
import {computed, getCurrentInstance, onMounted, ref} from "vue";
import axios from "axios";
import {useRoute} from "vue-router";
import {Location, OfficeBuilding} from "@element-plus/icons-vue";
import type {ComponentSize} from "element-plus";
import defaultimage from '@/assets/logo.svg'

const v3 = getCurrentInstance()
const eturl = v3.appContext.config.globalProperties.$url
const route=useRoute()
const stu = ref({sch:{},pics:[]})
const queryStuInfo = ()=>{
  axios.get('/queryStuInfoById',{params:{sid:route.params.sid}})
      .then(resp=>{
        console.log('详情数据',resp)
        stu.value = resp.data.data
      })
}
onMounted(()=>{
  queryStuInfo()
})
const size = ref<ComponentSize>('default')
const iconStyle = computed(() => {
  const marginMap = {
    large: '8px',
    default: '6px',
    small: '4px'
  }
  return {
    marginRight: marginMap[size.value] || marginMap.default,
  }
})
</script>

<template>
  <el-card class="box - card">
    <el-descriptions
        class="margin - top"
        title="学生详情"
        :column="3"
        size="default"
        border
    >
      <el-descriptions-item>
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <user/>
            </el-icon>
            姓名
          </div>
        </template>
        {{ stu.name }}
      </el-descriptions-item>//姓名
      <el-descriptions-item>
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <iphone/>
            </el-icon>
            年龄
          </div>
        </template>
        {{ stu.age }}
      </el-descriptions-item>//年龄
      <el-descriptions-item>
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <location/>
            </el-icon>
            生日
          </div>
        </template>
        {{ stu.birth }}
      </el-descriptions-item>//生日
      <el-descriptions-item>
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <tickets/>
            </el-icon>
            邮箱
          </div>
        </template>
        <el-tag size="small">{{stu.email}}</el-tag>
      </el-descriptions-item>//邮箱
      <el-descriptions-item>
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <office-building/>
            </el-icon>
            电话
          </div>
        </template>
        {{ stu.phone }}
      </el-descriptions-item>//电话
      <el-descriptions-item>
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <office-building/>
            </el-icon>
            所在学校
          </div>
        </template>
        {{ stu.sch.name }}
      </el-descriptions-item>//所在学校
      <el-descriptions-item>
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <office-building/>
            </el-icon>
            学校电话
          </div>
        </template>
        {{ stu.sch.phone }}
      </el-descriptions-item>//学校电话
      <el-descriptions-item :span="2">
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <office-building/>
            </el-icon>
            学校地址
          </div>
        </template>
        {{ stu.sch.info }}
      </el-descriptions-item>//学校地址
      <el-descriptions-item :span="3">
        <template #label>
          <div class="cell - item">
            <el-icon :style="iconStyle">
              <office-building/>
            </el-icon>
            头像列表
          </div>
        </template>
        <el-row>
          <el-col :span="6" v-for="(item,index) in stu.pics" :key="index">
            <el-card style="max-width: 480px">
              <template #header>{{ item.realname.substring(0,item.realname.lastIndexOf('.')) }}</template>
              <img
                  :src="item?eturl+item.savepath:defaultimage"
                  style="width: 100%"
              />

            </el-card>
          </el-col>
        </el-row>
      </el-descriptions-item>//头像列表
    </el-descriptions>
  </el-card>
</template>
<style scoped>

</style>