import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      children: [
        {
          path:'/stu',
          name:'/Stu',
          component:()=>import('@/views/stu/stu/StuIndex.vue'),
        },
        {
          path: '/stuinfo/:sid',
          name: '/StuInfo',
          component: () => import('@/views/stu/stu/components/StuInfo.vue')
        },
        {
          path: '/echarts',
          name: '/Echarts',
          component: () => import('@/views/stu/stu/components/Echarts.vue')
        }
      ]
    }
  ],
})

export default router
