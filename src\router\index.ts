import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      children: [
        {
          path:'/stu',
          name:'/Stu',
          component:()=>import('@/views/stu/stu/StuIndex.vue'),
        },
        {
          path: '/stuinfo/:sid',
          name: '/StuInfo',
          component: () => import('@/views/stu/stu/components/StuInfo.vue')
        },
        {
          path: '/echarts',
          name: '/Echarts',
          component: () => import('@/views/stu/stu/components/Echarts.vue')
        },
        {
          path: '/pro',
          name: '/Project',
          component: () => import('@/views/project/ProjectIndex.vue')
        },
        {
          path: '/ren',
          name: '/Personnel',
          component: () => import('@/views/personnel/PersonnelIndex.vue')
        },
        {
          path: '/test',
          name: '/Test',
          component: () => import('@/views/project/TestConnection.vue')
        },
        {
          path: '/cascader-test',
          name: '/CascaderTest',
          component: () => import('@/views/project/CascaderTest.vue')
        },
        {
          path: '/simple-test',
          name: '/SimpleTest',
          component: () => import('@/views/project/SimpleTest.vue')
        }
      ]
    }
  ],
})

export default router
