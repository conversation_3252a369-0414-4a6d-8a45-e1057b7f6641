<template>
  <div class="project-container">
    <div class="header-section">
      <h2>项目信息维护</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加项目
      </el-button>
    </div>

    <!-- 查询区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="项目名称">
          <el-input v-model="searchForm.name" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="所在城市">
          <el-input v-model="searchForm.province" placeholder="请输入城市" clearable />
        </el-form-item>
        <el-form-item label="负责人">
          <el-input v-model="searchForm.fuzeren" placeholder="请输入负责人" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 项目列表表格 -->
    <div class="table-section">
      <el-table :data="projectList" border style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="项目名称" min-width="150" />
        <el-table-column label="所在城市" width="120">
          <template #default="{ row }">
            {{ row.province || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="adress" label="详细地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="xuqiu" label="技术需求" min-width="150" show-overflow-tooltip />
        <el-table-column prop="startTime" label="开始时间" width="120" />
        <el-table-column prop="endTime" label="结束时间" width="120" />
        <el-table-column prop="nums" label="需求人数" width="100" />
        <el-table-column prop="salary" label="报价(人月)" width="120" />
        <el-table-column prop="fuzeren" label="负责人" width="120" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在城市" prop="province">
              <el-input v-model="formData.province" placeholder="请输入所在城市，如：济南" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="详细地址" prop="adress">
          <PcaCascader
            v-model="formData.regionPath"
            :full-address="formData.adress"
            @change="handleAddressChange"
          />
          <!-- 调试信息 -->
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            当前地址：{{ formData.adress || '未选择' }}
          </div>
        </el-form-item>

        <el-form-item label="技术需求" prop="xuqiu">
          <el-input
            v-model="formData.xuqiu"
            placeholder="请输入技术需求"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                type="date"
                placeholder="选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :teleported="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                type="date"
                placeholder="选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :teleported="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="需求人数" prop="nums">
              <el-input-number
                v-model="formData.nums"
                :min="1"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报价(人月)" prop="salary">
              <el-input-number
                v-model="formData.salary"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="负责人" prop="fuzeren">
          <el-input v-model="formData.fuzeren" placeholder="请输入负责人姓名" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { projectApi, type Project, type ProjectForm } from '@/api/project'
import PcaCascader from '@/components/PcaCascader.vue'
import axios from 'axios'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('添加项目')
const formRef = ref()

// 搜索表单（使用后端字段名）
const searchForm = reactive({
  name: '',        // 项目名称
  province: '',    // 所在城市
  fuzeren: ''      // 负责人
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 项目列表
const projectList = ref<Project[]>([])

// 表单数据（使用后端实体类字段名）
const formData = reactive<ProjectForm>({
  name: '',           // 项目名称
  province: '',       // 省份 - 存储完整的省市区信息
  adress: '',         // 详细地址
  xuqiu: '',          // 技术需求
  startTime: '',
  endTime: '',
  nums: 1,            // 需求人数
  salary: 0,          // 报价(人月)
  fuzeren: '',        // 负责人
  // 前端扩展字段
  selectedProvince: '',
  selectedCity: '',
  selectedDistrict: '',
  regionPath: []
})

// 表单验证规则（使用后端字段名）
const formRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  province: [
    { required: true, message: '请选择所在地区', trigger: 'change' }
  ],
  adress: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ],
  xuqiu: [
    { required: true, message: '请输入技术需求', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  nums: [
    { required: true, message: '请输入需求人数', trigger: 'blur' }
  ],
  salary: [
    { required: true, message: '请输入报价', trigger: 'blur' }
  ],
  fuzeren: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ]
}



// 模拟数据（使用后端字段名）
const mockData: Project[] = [
  {
    id: 1,
    name: '智慧城市管理系统',
    province: '济南',
    adress: '山东省-济南市-历下区-趵北路6号-智慧大厦',
    xuqiu: 'Vue3 + TypeScript + Element Plus + Spring Boot',
    startTime: '2024-01-15',
    endTime: '2024-06-30',
    nums: 5,
    salary: 50000,
    fuzeren: '张三'
  },
  {
    id: 2,
    name: '电商平台开发',
    province: '青岛',
    adress: '山东省-青岛市-市南区-香港中路100号-科技园',
    xuqiu: 'React + Node.js + MySQL + Redis',
    startTime: '2024-02-01',
    endTime: '2024-08-31',
    nums: 8,
    salary: 80000,
    fuzeren: '李四'
  }
]

// 方法定义
const loadProjectList = async () => {
  loading.value = true
  try {
    // 调用后端getAllProjects接口获取项目列表
    console.log('正在获取项目列表...')
    const response = await projectApi.getAllProjects()
    console.log('获取项目列表响应:', response)

    if (response.data) {
      // 直接使用返回的数组数据
      projectList.value = Array.isArray(response.data) ? response.data : []
      pagination.total = projectList.value.length
      console.log('项目列表加载成功，共', pagination.total, '个项目')
    } else {
      projectList.value = []
      pagination.total = 0
    }

  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败，使用本地数据')
    // 如果后端调用失败，使用模拟数据
    projectList.value = mockData
    pagination.total = mockData.length
  } finally {
    loading.value = false
  }
}

const handleSearch = async () => {
  loading.value = true
  try {
    // 先获取所有项目，然后在前端进行过滤
    const response = await projectApi.getAllProjects()

    if (response.data && Array.isArray(response.data)) {
      let filteredList = response.data

      // 根据搜索条件过滤
      if (searchForm.name) {
        filteredList = filteredList.filter(item =>
          item.name.includes(searchForm.name)
        )
      }
      if (searchForm.province) {
        filteredList = filteredList.filter(item =>
          item.province.includes(searchForm.province)
        )
      }
      if (searchForm.fuzeren) {
        filteredList = filteredList.filter(item =>
          item.fuzeren.includes(searchForm.fuzeren)
        )
      }

      projectList.value = filteredList
      pagination.total = filteredList.length
    }
  } catch (error) {
    console.error('搜索项目失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.province = ''
  searchForm.fuzeren = ''
  pagination.currentPage = 1
  loadProjectList()
}

const handleAdd = () => {
  dialogTitle.value = '添加项目'
  resetFormData()
  dialogVisible.value = true
}

const handleEdit = (row: Project) => {
  dialogTitle.value = '编辑项目'

  Object.assign(formData, {
    ...row,
    // 重置前端扩展字段
    selectedProvince: '',
    selectedCity: '',
    selectedDistrict: '',
    regionPath: []
  })

  dialogVisible.value = true
}

const handleDelete = (row: Project) => {
  ElMessageBox.confirm(
    `确定要删除项目"${row.name}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      loading.value = true

      console.log('准备删除项目，ID:', row.id)
      console.log('请求URL:', `${axios.defaults.baseURL}/pro/deleteProject`)

      // 调用删除API
      const response = await projectApi.deleteProject(row.id!)
      console.log('删除项目响应:', response)

      if (response.data === 1 || response.data > 0) {
        ElMessage.success('删除成功')
        // 重新加载项目列表
        await loadProjectList()
      } else {
        ElMessage.error('删除失败')
      }
    } catch (error: any) {
      console.error('删除项目失败:', error)
      ElMessage.error(`删除失败: ${error.response?.data?.message || error.message || '请重试'}`)
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消删除
    console.log('用户取消删除操作')
  })
}

const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true

        // 准备提交的数据，只包含后端需要的字段
        const submitData: Project = {
          id: formData.id,
          name: formData.name,
          province: formData.province, // 这里存储的是完整的省市区信息
          adress: formData.adress,
          xuqiu: formData.xuqiu,
          startTime: formData.startTime,
          endTime: formData.endTime,
          nums: formData.nums,
          salary: formData.salary,
          fuzeren: formData.fuzeren
        }

        if (dialogTitle.value === '添加项目') {
          // 添加项目
          console.log('准备添加项目:', submitData)
          console.log('请求URL:', `${axios.defaults.baseURL}/pro/addProject`)

          const response = await projectApi.addProject(submitData)
          console.log('添加项目响应:', response)

          if (response.data === 1 || response.data > 0) {
            ElMessage.success('添加项目成功')
            dialogVisible.value = false
            // 重新加载项目列表
            await loadProjectList()
          } else {
            ElMessage.error('添加项目失败')
          }
        } else {
          // 编辑项目
          console.log('准备编辑项目:', submitData)
          console.log('请求URL:', `${axios.defaults.baseURL}/pro/updateProject`)

          const response = await projectApi.updateProject(submitData)
          console.log('编辑项目响应:', response)

          if (response.data === 1 || response.data > 0) {
            ElMessage.success('修改项目成功')
            dialogVisible.value = false
            // 重新加载项目列表
            await loadProjectList()
          } else {
            ElMessage.error('修改项目失败')
          }
        }
      } catch (error: any) {
        console.error('提交项目失败:', error)
        ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请检查网络连接'}`)
      } finally {
        loading.value = false
      }
    }
  })
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
}

// 处理地址选择变化（级联选择器现在用于详细地址）
const handleAddressChange = (fullRegion: string, province: string, city: string, district: string) => {
  // 将完整的省市区信息存储到 adress 字段
  formData.adress = fullRegion

  // 更新前端扩展字段
  formData.selectedProvince = province
  formData.selectedCity = city
  formData.selectedDistrict = district

  console.log('地址选择变化:', {
    fullRegion,
    province,
    city,
    district,
    storedAddress: formData.adress
  })
}

const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    province: '',
    adress: '',
    xuqiu: '',
    startTime: '',
    endTime: '',
    nums: 1,
    salary: 0,
    fuzeren: '',
    // 重置前端扩展字段
    selectedProvince: '',
    selectedCity: '',
    selectedDistrict: '',
    regionPath: []
  })
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadProjectList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadProjectList()
}

// 生命周期
onMounted(() => {
  loadProjectList()
})
</script>

<style scoped>
.project-container {
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-section {
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
