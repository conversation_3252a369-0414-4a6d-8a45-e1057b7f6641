<template>
  <div class="project-container">
    <div class="header-section">
      <h2>项目信息维护</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加项目
      </el-button>
    </div>

    <!-- 查询区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="项目名称">
          <el-input v-model="searchForm.projectName" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="所在城市">
          <el-input v-model="searchForm.city" placeholder="请输入城市" clearable />
        </el-form-item>
        <el-form-item label="负责人">
          <el-input v-model="searchForm.manager" placeholder="请输入负责人" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 项目列表表格 -->
    <div class="table-section">
      <el-table :data="projectList" border style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="projectName" label="项目名称" min-width="150" />
        <el-table-column prop="city" label="所在城市" width="120" />
        <el-table-column prop="address" label="详细地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="techRequirement" label="技术需求" min-width="150" show-overflow-tooltip />
        <el-table-column prop="startTime" label="开始时间" width="120" />
        <el-table-column prop="endTime" label="结束时间" width="120" />
        <el-table-column prop="requiredPersons" label="需求人数" width="100" />
        <el-table-column prop="price" label="报价(人月)" width="120" />
        <el-table-column prop="manager" label="负责人" width="120" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="formData.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在城市" prop="city">
              <el-input v-model="formData.city" placeholder="如：济南" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="详细地址" prop="address">
          <el-input 
            v-model="formData.address" 
            placeholder="如：山东省-济南市-历下区-趵北路6号-xxx" 
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item label="技术需求" prop="techRequirement">
          <el-input 
            v-model="formData.techRequirement" 
            placeholder="请输入技术需求"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                type="date"
                placeholder="选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                type="date"
                placeholder="选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="需求人数" prop="requiredPersons">
              <el-input-number 
                v-model="formData.requiredPersons" 
                :min="1" 
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报价(人月)" prop="price">
              <el-input-number 
                v-model="formData.price" 
                :min="0" 
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="负责人" prop="manager">
          <el-input v-model="formData.manager" placeholder="请输入负责人姓名" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { projectApi, type Project } from '@/api/project'
import axios from 'axios'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('添加项目')
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  projectName: '',
  city: '',
  manager: ''
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 项目列表
const projectList = ref<Project[]>([])

// 表单数据
const formData = reactive<Project>({
  projectName: '',
  city: '',
  address: '',
  techRequirement: '',
  startTime: '',
  endTime: '',
  requiredPersons: 1,
  price: 0,
  manager: ''
})

// 表单验证规则
const formRules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  city: [
    { required: true, message: '请输入所在城市', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ],
  techRequirement: [
    { required: true, message: '请输入技术需求', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  requiredPersons: [
    { required: true, message: '请输入需求人数', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入报价', trigger: 'blur' }
  ],
  manager: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ]
}



// 模拟数据（作为后备数据）
const mockData: Project[] = [
  {
    id: 1,
    projectName: '智慧城市管理系统',
    city: '济南',
    address: '山东省-济南市-历下区-趵北路6号-智慧大厦',
    techRequirement: 'Vue3 + TypeScript + Element Plus + Spring Boot',
    startTime: '2024-01-15',
    endTime: '2024-06-30',
    requiredPersons: 5,
    price: 50000,
    manager: '张三'
  },
  {
    id: 2,
    projectName: '电商平台开发',
    city: '青岛',
    address: '山东省-青岛市-市南区-香港中路100号-科技园',
    techRequirement: 'React + Node.js + MySQL + Redis',
    startTime: '2024-02-01',
    endTime: '2024-08-31',
    requiredPersons: 8,
    price: 80000,
    manager: '李四'
  }
]

// 方法定义
const loadProjectList = async () => {
  loading.value = true
  try {
    // 暂时使用模拟数据，因为后端还没有getProjectList接口
    // TODO: 等后端添加getProjectList接口后，取消注释下面的代码
    /*
    const response = await projectApi.getProjectList({
      page: pagination.currentPage,
      size: pagination.pageSize
    })

    if (response.data) {
      projectList.value = response.data.data || response.data
      pagination.total = response.data.total || response.data.length
    }
    */

    // 使用模拟数据
    setTimeout(() => {
      projectList.value = mockData
      pagination.total = mockData.length
      loading.value = false
    }, 500)

  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败，使用本地数据')
    // 如果后端调用失败，使用模拟数据
    projectList.value = mockData
    pagination.total = mockData.length
    loading.value = false
  }
}

const handleSearch = async () => {
  loading.value = true
  try {
    // 构建搜索参数
    const searchParams = {
      projectName: searchForm.projectName,
      city: searchForm.city,
      manager: searchForm.manager,
      page: pagination.currentPage,
      size: pagination.pageSize
    }

    // 调用搜索API
    const response = await projectApi.searchProjects(searchParams)

    if (response.data) {
      projectList.value = response.data.data || response.data
      pagination.total = response.data.total || response.data.length
    }
  } catch (error) {
    console.error('搜索项目失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  searchForm.projectName = ''
  searchForm.city = ''
  searchForm.manager = ''
  pagination.currentPage = 1
  loadProjectList()
}

const handleAdd = () => {
  dialogTitle.value = '添加项目'
  resetFormData()
  dialogVisible.value = true
}

const handleEdit = (row: Project) => {
  dialogTitle.value = '编辑项目'
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = (row: Project) => {
  ElMessageBox.confirm(
    `确定要删除项目"${row.projectName}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      loading.value = true

      // 调用删除API
      const response = await projectApi.deleteProject(row.id!)

      if (response.data === 1 || response.data > 0) {
        ElMessage.success('删除成功')
        loadProjectList()
      } else {
        ElMessage.error('删除失败')
      }
    } catch (error) {
      console.error('删除项目失败:', error)
      ElMessage.error('删除失败，请重试')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消删除
  })
}

const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true

        if (dialogTitle.value === '添加项目') {
          // 添加项目
          console.log('准备添加项目:', formData)
          console.log('请求URL:', `${axios.defaults.baseURL}/pro/addProject`)

          const response = await projectApi.addProject(formData)
          console.log('添加项目响应:', response)

          if (response.data === 1 || response.data > 0) {
            ElMessage.success('添加项目成功')
            dialogVisible.value = false
            loadProjectList()
          } else {
            ElMessage.error('添加项目失败')
          }
        } else {
          // 编辑项目
          console.log('准备编辑项目:', formData)

          const response = await projectApi.updateProject(formData)
          console.log('编辑项目响应:', response)

          if (response.data === 1 || response.data > 0) {
            ElMessage.success('修改项目成功')
            dialogVisible.value = false
            loadProjectList()
          } else {
            ElMessage.error('修改项目失败')
          }
        }
      } catch (error) {
        console.error('提交项目失败:', error)
        ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请检查网络连接'}`)
      } finally {
        loading.value = false
      }
    }
  })
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
}

const resetFormData = () => {
  Object.assign(formData, {
    projectName: '',
    city: '',
    address: '',
    techRequirement: '',
    startTime: '',
    endTime: '',
    requiredPersons: 1,
    price: 0,
    manager: ''
  })
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadProjectList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadProjectList()
}

// 生命周期
onMounted(() => {
  loadProjectList()
})
</script>

<style scoped>
.project-container {
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-section {
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
