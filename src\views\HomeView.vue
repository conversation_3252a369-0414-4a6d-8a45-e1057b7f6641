<script setup lang="ts">
import TheWelcome from '../components/TheWelcome.vue'
import {useRoute} from "vue-router";
const route = useRoute()
</script>

<template>
  <el-container>
    <el-header>
      <h1>学生选课系统</h1>
      <div class="header-right">
        <!--            头像 下拉框-->

        <el-avatar style="margin-right:10px;"
          src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        />
        <el-dropdown>
        <span class="el-dropdown-link">
          Dropdown List
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>Action 1</el-dropdown-item>
              <el-dropdown-item>Action 2</el-dropdown-item>
              <el-dropdown-item>Action 3</el-dropdown-item>
              <el-dropdown-item disabled>Action 4</el-dropdown-item>
              <el-dropdown-item divided>Action 5</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    <el-container>
      <el-aside width="200px">
        <el-menu
            active-text-color="#ffd04b"
            background-color="#ba2f7b"
            class="el-menu-vertical-demo"
            :default-active="route.path"
            text-color="#fff"
            router
        >
          <el-sub-menu index="1">
            <template #title>
              <el-icon><Stamp /></el-icon>
              <span>学生管理</span>
            </template>
            <el-menu-item index="/pro">项目信息维护</el-menu-item>
            <el-menu-item index="/ren">人员信息维护</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="2">
            <template #title>
              <el-icon><School /></el-icon>
              <span>学校管理</span>
            </template>
            <el-menu-item index="">学校列表</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="3">
            <template #title>
              <el-icon><DataAnalysis /></el-icon>
              <span>数据统计</span>
            </template>
            <el-menu-item index="/echarts">图形展示</el-menu-item>
          </el-sub-menu>

        </el-menu>
      </el-aside>

      <el-container>
        <el-main><router-view></router-view></el-main>
        <el-footer style="display:flex;align-items: center;justify-content: center;background-color: #1e131d;color:white;">ET2501</el-footer>
      </el-container>
    </el-container>
  </el-container>

</template>
<style scoped>
.el-container {
  height: 100%;
}
.el-header {
  background-color: #36282b;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-right {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.el-dropdown {
  color:white;
}
.el-aside{
  background-color: #ba2f7b;
}
.el-menu{
  border:0;
}
</style>
