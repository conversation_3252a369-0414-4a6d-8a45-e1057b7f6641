<script setup lang="ts">
import axios from 'axios'
import { ref } from 'vue'
import * as echarts from 'echarts'

const echart = ref()

const option = {
    xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: {
        type: 'value'
    },
    series: [
        {
            data: [120, 200, 150, 80, 70, 110, 130],
            type: 'bar'
        }
    ]
}
const result = ref < Array<any>([])
const getEchartsData = () => {
    axios.get('/querygetEchartsData').then(resp => {
        if (resp.data.code == 200) {
            result.value = resp.data.data
            result.value.forEach(r => {
                option.xAxis.data.push(r.name)
                option.series[0].data.push(r.rs)
            })
            const myChart = echarts.init(echart.value);
            option && myChart.setOption(option);
        }
    })
} 
</script>

<template>
    <div ref="echart" style="width: 100;height: 400px;">

    </div>
</template>

<style scoped></style>