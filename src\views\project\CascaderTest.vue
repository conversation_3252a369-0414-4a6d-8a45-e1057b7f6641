<template>
  <div class="cascader-test">
    <el-card>
      <h2>省市区级联选择器测试</h2>
      
      <el-form :model="testForm" label-width="120px" style="max-width: 600px;">
        <el-form-item label="选择地区">
          <PcaCascader 
            v-model="testForm.regionPath"
            @change="handleRegionChange"
          />
        </el-form-item>
        
        <el-form-item label="详细地址">
          <el-input 
            v-model="testForm.detailAddress" 
            placeholder="请输入详细地址"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitTest">提交测试</el-button>
          <el-button @click="resetTest">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-divider />
      
      <h3>选择结果：</h3>
      <div class="result-display">
        <p><strong>完整地区：</strong>{{ testResult.fullRegion || '未选择' }}</p>
        <p><strong>省份：</strong>{{ testResult.province || '未选择' }}</p>
        <p><strong>城市：</strong>{{ testResult.city || '未选择' }}</p>
        <p><strong>区县：</strong>{{ testResult.district || '未选择' }}</p>
        <p><strong>详细地址：</strong>{{ testForm.detailAddress || '未填写' }}</p>
        <p><strong>级联路径：</strong>{{ JSON.stringify(testForm.regionPath) }}</p>
      </div>
      
      <el-divider />
      
      <h3>模拟后端数据格式：</h3>
      <div class="backend-format">
        <pre>{{ JSON.stringify(backendData, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import PcaCascader from '@/components/PcaCascader.vue'

// 测试表单数据
const testForm = reactive({
  regionPath: [] as string[],
  detailAddress: ''
})

// 测试结果
const testResult = reactive({
  fullRegion: '',
  province: '',
  city: '',
  district: ''
})

// 模拟后端数据格式
const backendData = computed(() => ({
  name: '测试项目',
  province: testResult.fullRegion, // 后端存储完整的省市区
  adress: testForm.detailAddress,  // 后端存储详细地址
  xuqiu: 'Vue3 + Spring Boot',
  startTime: '2024-01-01',
  endTime: '2024-12-31',
  nums: 3,
  salary: 30000,
  fuzeren: '测试负责人'
}))

// 处理地区选择变化
const handleRegionChange = (fullRegion: string, province: string, city: string, district: string) => {
  testResult.fullRegion = fullRegion
  testResult.province = province
  testResult.city = city
  testResult.district = district
  
  console.log('地区选择变化:', { fullRegion, province, city, district })
}

// 提交测试
const submitTest = () => {
  if (!testResult.fullRegion) {
    ElMessage.warning('请先选择地区')
    return
  }
  
  console.log('提交的数据:', backendData.value)
  ElMessage.success('测试提交成功，请查看控制台输出')
}

// 重置测试
const resetTest = () => {
  testForm.regionPath = []
  testForm.detailAddress = ''
  testResult.fullRegion = ''
  testResult.province = ''
  testResult.city = ''
  testResult.district = ''
  ElMessage.info('已重置')
}
</script>

<style scoped>
.cascader-test {
  padding: 20px;
}

.result-display {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.result-display p {
  margin: 8px 0;
  font-size: 14px;
}

.backend-format {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.backend-format pre {
  margin: 0;
  white-space: pre-wrap;
}
</style>
