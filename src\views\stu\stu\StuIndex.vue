<template>

  <el-card>
    <el-tabs
        v-model="activeName"
        class="demo-tabs"
    >
      <el-tab-pane label="学生添加" name="first"><StuAdd/></el-tab-pane>
      <el-tab-pane label="学生列表" name="second"><StuList/></el-tab-pane>

    </el-tabs>
  </el-card>
</template>

<script setup lang="ts">
import StuAdd from "@/views/stu/stu/components/StuAdd.vue";
import StuList from "@/views/stu/stu/components/StuList.vue";
import {ref} from 'vue'
const activeName = ref('first')
</script>

<style scoped>

</style>